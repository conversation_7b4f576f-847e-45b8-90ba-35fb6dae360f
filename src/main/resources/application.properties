#spring.application.name=market-stream-comparison-tool

#spring.main.web-application-type=none
#spring.main.allow-bean-definition-overriding=true
#spring.autoconfigure.exclude= org.springframework.boot.actuate.autoconfigure.health.HealthEndpointConfiguration
# would be nice if this would not be needed
#
## Tracing
#tracing.level=10
#tracing.buffer.size=2048
#tracing.stats.age=1000
#tracing.stats.size=1048
#
## StatsE
#statse.enabled=false
#statse.agent.address=tcp://127.0.0.1:14444
#statse.queue.size=1000

spring.jmx.enabled=true

management.endpoints.web.exposure.include=*
management.endpoints.jmx.exposure.include=*
management.endpoint.health.show-details=always


hawtio.authenticationEnabled=false