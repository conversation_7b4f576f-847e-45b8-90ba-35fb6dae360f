package com.flutter.marketstreamcomparisontool.client;

import com.betfair.platform.fms.cache.KafkaMarketViewCache;
import com.ppb.platform.sb.fmg.MarketStreamClient;
import com.ppb.platform.sb.toggle.feature.FeatureToggle;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.SmartLifecycle;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.google.common.base.Preconditions.checkArgument;

@Slf4j
@Component
public class MarketStreamServiceManager implements SmartLifecycle {

    private static final int PHASE = 0;

    private final KafkaMarketViewCache marketStreamCacheInitializerGSSP;
    private final KafkaMarketViewCache marketStreamCacheInitializerFMG;
    private final FeatureToggle consumerToggleGSSP;
    private final FeatureToggle consumerToggleFMG;

    public MarketStreamServiceManager(KafkaMarketViewCache marketStreamCacheInitializerGSSP,
                                      KafkaMarketViewCache marketStreamCacheInitializerFMG,
                                      FeatureToggle consumerToggleGSSP,
                                      FeatureToggle consumerToggleFMG) {
        checkArgument(marketStreamCacheInitializerGSSP != null, "marketStreamCacheInitializerGSSP is mandatory");
        checkArgument(marketStreamCacheInitializerFMG != null, "marketStreamCacheInitializerFMG is mandatory");
        checkArgument(consumerToggleGSSP != null, "consumerToggleGSSP is mandatory");
        checkArgument(consumerToggleFMG != null, "consumerToggleFMG is mandatory");
        this.marketStreamCacheInitializerGSSP = marketStreamCacheInitializerGSSP;
        this.marketStreamCacheInitializerFMG = marketStreamCacheInitializerFMG;
        this.consumerToggleGSSP = consumerToggleGSSP;
        this.consumerToggleFMG = consumerToggleFMG;
    }

    @Override
    public void stop(@NotNull Runnable runnable) {
        this.stop();
    }

    @Override
    public void start() {
        log.info("operation='start', msg='starting market stream cache service'");

        Set<KafkaMarketViewCache> marketStreamClients = new HashSet<>();

        if (consumerToggleGSSP.isEnabled()) {
            marketStreamClients.add(marketStreamCacheInitializerGSSP);
        }

        if (consumerToggleFMG.isEnabled()) {
            marketStreamClients.add(marketStreamCacheInitializerFMG);
        }

        ExecutorService executorService = Executors.newFixedThreadPool(marketStreamClients.size());

        List<Future<?>> futures = marketStreamClients.stream()
                .map((client) -> executorService.submit(() -> {
                    try {
                        client.start();
                    } catch (Exception e) {
                        log.error("operation='start', msg='failed starting market stream cache service'", e);
                    }
                    return null;
                }))
                .collect(Collectors.toList());


        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("operation='start', msg='failed starting market stream cache service'", e);
                throw new RuntimeException(e);
            }
        }

        executorService.shutdown();
    }

    @Override
    public void stop() {
        log.info("operation='stop', msg='stopping market stream cache service'");
        if (marketStreamCacheInitializerGSSP.isRunning()) {
            marketStreamCacheInitializerGSSP.stop();
        }
        if (marketStreamCacheInitializerFMG.isRunning()) {
            marketStreamCacheInitializerFMG.stop();
        }
    }

    @Override
    public boolean isRunning() {
        return (!consumerToggleGSSP.isEnabled() || marketStreamCacheInitializerGSSP.isRunning()) &&
                (!consumerToggleFMG.isEnabled() || marketStreamCacheInitializerFMG.isRunning());

    }

    @Override
    public int getPhase() {
        return PHASE;
    }
}

