package com.flutter.marketstreamcomparisontool.client;

import com.ppb.platform.sb.fmg.MarketStreamClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;

import static com.ppb.platform.sb.fmg.config.MarketStreamClientConfigUtil.getMarketStreamClientConfig;

@Configuration
@ImportResource("classpath:conf/fms-new-client-application.xml")
public class MarketStreamConfiguration {

    @Bean
    public MarketStreamClient.MarketStreamClientBuilder marketStreamClientBuilder(@Value("${fms.client.config}") String marketStreamClientConfig) {
        return new MarketStreamClient.MarketStreamClientBuilder(getMarketStreamClientConfig(marketStreamClientConfig));
    }



}
